#!/bin/bash

# Script para construir y ejecutar el contenedor BMC Remedy

echo "========================================"
echo "BMC Remedy Container - Script de Ejecución"
echo "========================================"

# Verificar si Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker no está instalado o no está en el PATH"
    exit 1
fi

# Verificar si docker-compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo "ERROR: Docker Compose no está instalado o no está en el PATH"
    exit 1
fi

# Verificar si existe el archivo .env
if [ ! -f .env ]; then
    echo "ADVERTENCIA: No se encontró el archivo .env"
    echo "Copiando .env.example a .env..."
    cp .env.example .env
    echo ""
    echo "IMPORTANTE: Edite el archivo .env con sus credenciales antes de continuar"
    echo "Presione Enter para continuar o Ctrl+C para salir..."
    read
fi

echo ""
echo "Construyendo la imagen Docker..."
docker-compose build

if [ $? -ne 0 ]; then
    echo "ERROR: Falló la construcción de la imagen"
    exit 1
fi

echo ""
echo "Imagen construida exitosamente!"
echo ""
echo "Ejemplos de uso:"
echo ""
echo "1. Consulta básica:"
echo "   docker-compose run --rm bmc-remedy-app python main.py 1152213619"
echo ""
echo "2. Con destinatario específico:"
echo "   docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL>"
echo ""
echo "3. Mostrar todos los incidentes:"
echo "   docker-compose run --rm bmc-remedy-app python main.py 1152213619 --todos"
echo ""
echo "4. Probar conversión de fechas:"
echo "   docker-compose run --rm bmc-remedy-app python test_fecha_colombia.py"
echo ""

# Preguntar si quiere ejecutar una consulta de prueba
read -p "¿Desea ejecutar una consulta de prueba? (s/n): " ejecutar
if [[ $ejecutar == "s" || $ejecutar == "S" ]]; then
    read -p "Ingrese el número de cédula: " cedula
    read -p "Ingrese el email destinatario (opcional): " email
    
    if [ -z "$email" ]; then
        echo "Ejecutando: docker-compose run --rm bmc-remedy-app python main.py $cedula"
        docker-compose run --rm bmc-remedy-app python main.py $cedula
    else
        echo "Ejecutando: docker-compose run --rm bmc-remedy-app python main.py $cedula --destinatario $email"
        docker-compose run --rm bmc-remedy-app python main.py $cedula --destinatario $email
    fi
fi

echo ""
echo "Script completado."
