version: '3.8'

services:
  bmc-remedy-app:
    build: .
    container_name: bmc-remedy-container
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      # Montar directorio de logs para persistencia
      - ./logs:/app/logs
      # Montar archivo .env si existe
      - ./.env:/app/.env:ro
    networks:
      - bmc-network
    # Comando por defecto - se puede sobrescribir al ejecutar
    command: ["python", "main.py", "--help"]
    
  # Servicio opcional para desarrollo/debugging
  bmc-remedy-dev:
    build: .
    container_name: bmc-remedy-dev
    environment:
      - PYTHONUNBUFFERED=1
      - DEBUG=true
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
      # Montar código fuente para desarrollo
      - .:/app
    networks:
      - bmc-network
    # Mantener el contenedor corriendo para debugging
    command: ["tail", "-f", "/dev/null"]
    profiles:
      - dev

networks:
  bmc-network:
    driver: bridge
