#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo optimizado para procesar y enviar reportes por correo.
Simplificado para mejor rendimiento y mantenibilidad.
"""

import argparse
import os
import sys
import json
from datetime import datetime
from pathlib import Path

# Import custom modules
from logging_config import setup_logging, get_logger
import enviar_reporte


def obtener_email_destinatario(cedula: str, destinatario: str = None) -> str:
    """
    Obtiene el email del destinatario desde el reporte o usa el proporcionado.
    
    Args:
        cedula: Número de cédula
        destinatario: Email proporcionado (opcional)
        
    Returns:
        Email del destinatario
    """
    logger = get_logger('procesar_todo.obtener_email')
    
    if destinatario:
        return destinatario
    
    # Intentar obtener el email del usuario del reporte
    reporte_file = Path(__file__).parent / "reporte_unificado.json"
    
    if reporte_file.exists():
        try:
            with open(reporte_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)

            user_info = report_data.get("user_info", {})
            user_email = user_info.get("Internet E-mail", "").strip()
            
            if user_email and user_email != "N/A":
                logger.info(f"Usando email del usuario: {user_email}")
                return user_email
            else:
                logger.warning("Usuario encontrado pero sin email válido")
                
        except Exception as e:
            logger.warning(f"Error al obtener email del usuario: {str(e)}")

    # Usar email por defecto
    email_default = "<EMAIL>"
    logger.info(f"Usando email por defecto: {email_default}")
    return email_default


def mostrar_reporte_en_consola(reporte_file: str):
    """
    Muestra el reporte en la consola usando el módulo open.
    
    Args:
        reporte_file: Ruta al archivo de reporte
    """
    logger = get_logger('procesar_todo.mostrar_reporte')
    
    try:
        # Importar y usar la función de mostrar reporte
        import open as open_module
        
        if hasattr(open_module, 'mostrar_reporte_unificado'):
            open_module.mostrar_reporte_unificado(reporte_file)
            logger.info("Visualización en consola completada")
        else:
            logger.warning("Función mostrar_reporte_unificado no encontrada")
            
    except Exception as e:
        logger.error(f"Error al mostrar en consola: {str(e)}")
        logger.info("Continuando con el proceso...")


def limpiar_archivos_temporales():
    """
    Elimina archivos temporales después del envío exitoso.
    """
    logger = get_logger('procesar_todo.limpiar')
    
    try:
        json_files = ['reporte_unificado.json', 'test_report.json']
        for json_file in json_files:
            file_path = Path(__file__).parent / json_file
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Archivo temporal eliminado: {json_file}")
    except Exception as e:
        logger.warning(f"Error al limpiar archivos temporales: {str(e)}")


def validar_reporte(reporte_file: str) -> bool:
    """
    Valida que el archivo de reporte existe y contiene datos.
    
    Args:
        reporte_file: Ruta al archivo de reporte
        
    Returns:
        True si el reporte es válido, False en caso contrario
    """
    logger = get_logger('procesar_todo.validar')
    
    if not os.path.exists(reporte_file):
        logger.error(f"El archivo de reporte no existe: {reporte_file}")
        return False
    
    try:
        with open(reporte_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Contar tickets e incidentes
        tickets_count = 0
        incidentes_count = 0
        
        if "tickets" in data and isinstance(data["tickets"], dict):
            tickets_data = data["tickets"].get("data", [])
            if isinstance(tickets_data, list):
                tickets_count = len(tickets_data)
        
        if "incidents" in data and isinstance(data["incidents"], dict):
            incidents_data = data["incidents"].get("data", [])
            if isinstance(incidents_data, list):
                incidentes_count = len(incidents_data)
        
        logger.info(f"Reporte válido - tickets: {tickets_count}, incidentes: {incidentes_count}")
        
        if tickets_count == 0 and incidentes_count == 0:
            logger.warning("El reporte no contiene tickets ni incidentes. El correo puede llegar vacío.")
        
        return True
        
    except Exception as e:
        logger.error(f"Error al validar el reporte: {str(e)}")
        return False


def procesar_y_enviar(cedula: str, destinatario: str = None) -> bool:
    """
    Procesa el reporte unificado existente y lo envía por correo.
    
    Args:
        cedula: Número de cédula a procesar
        destinatario: Correo del destinatario (opcional)
        
    Returns:
        True si el proceso fue exitoso, False en caso contrario
    """
    logger = get_logger('procesar_todo')
    
    try:
        logger.info(f"Procesando datos para cédula: {cedula}")
        
        # Verificar que existe el archivo de reporte
        reporte_file = os.path.join(os.path.dirname(__file__), "reporte_unificado.json")
        
        if not validar_reporte(reporte_file):
            return False
        
        # Mostrar el reporte en la consola
        mostrar_reporte_en_consola(reporte_file)
        
        # Obtener el destinatario del correo
        destinatario_final = obtener_email_destinatario(cedula, destinatario)
        
        # Enviar el reporte por correo
        logger.info(f"Enviando reporte a {destinatario_final} usando enviar_reporte.py...")
        enviado = enviar_reporte.enviar_reporte(reporte_file, destinatario_final)
        
        if enviado:
            logger.info(f"Reporte enviado correctamente a {destinatario_final}")
            
            # Limpiar archivos temporales después del envío exitoso
            limpiar_archivos_temporales()
            
            return True
        else:
            logger.error("No se confirmó el envío del reporte")
            return False
            
    except Exception as e:
        logger.error(f"Error en el procesamiento: {str(e)}")
        return False


def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Procesar y enviar reporte para una cédula')
    parser.add_argument('cedula', help='Número de cédula a procesar')
    parser.add_argument('--destinatario', '-d', help='Correo del destinatario (opcional)')
    
    args = parser.parse_args()
    
    try:
        # Setup logging
        setup_logging()
        logger = get_logger('procesar_todo')
        
        logger.info(f"Starting procesar_todo - cedula: {args.cedula}, destinatario: {args.destinatario}")
        
        # Ejecutar el procesamiento y enviar correo
        resultado = procesar_y_enviar(args.cedula, args.destinatario)
        
        # Salir con código de error si hubo problemas
        if not resultado:
            logger.error("El proceso terminó con errores.")
            sys.exit(1)
        else:
            logger.info(f"Proceso completado exitosamente para la cédula {args.cedula}")
            
    except KeyboardInterrupt:
        print("\nProceso interrumpido por el usuario")
        sys.exit(1)
    except Exception as e:
        try:
            logger = get_logger('procesar_todo')
        except:
            setup_logging()
            logger = get_logger('procesar_todo')
        
        logger.error(f"Error en procesar_todo: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
