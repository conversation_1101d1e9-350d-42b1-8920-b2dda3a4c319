@echo off
REM Script para construir y ejecutar el contenedor BMC Remedy

echo ========================================
echo BMC Remedy Container - Script de Ejecucion
echo ========================================

REM Verificar si Docker está instalado
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker no está instalado o no está en el PATH
    pause
    exit /b 1
)

REM Verificar si docker-compose está instalado
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Compose no está instalado o no está en el PATH
    pause
    exit /b 1
)

REM Verificar si existe el archivo .env
if not exist .env (
    echo ADVERTENCIA: No se encontró el archivo .env
    echo Copiando .env.example a .env...
    copy .env.example .env
    echo.
    echo IMPORTANTE: Edite el archivo .env con sus credenciales antes de continuar
    echo Presione cualquier tecla para continuar o Ctrl+C para salir...
    pause >nul
)

echo.
echo Construyendo la imagen Docker...
docker-compose build

if %errorlevel% neq 0 (
    echo ERROR: Falló la construcción de la imagen
    pause
    exit /b 1
)

echo.
echo Imagen construida exitosamente!
echo.
echo Ejemplos de uso:
echo.
echo 1. Consulta básica:
echo    docker-compose run --rm bmc-remedy-app python main.py 1152213619
echo.
echo 2. Con destinatario específico:
echo    docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL>
echo.
echo 3. Mostrar todos los incidentes:
echo    docker-compose run --rm bmc-remedy-app python main.py 1152213619 --todos
echo.
echo 4. Probar conversión de fechas:
echo    docker-compose run --rm bmc-remedy-app python test_fecha_colombia.py
echo.

REM Preguntar si quiere ejecutar una consulta de prueba
set /p ejecutar="¿Desea ejecutar una consulta de prueba? (s/n): "
if /i "%ejecutar%"=="s" (
    set /p cedula="Ingrese el número de cédula: "
    set /p email="Ingrese el email destinatario (opcional): "
    
    if "%email%"=="" (
        echo Ejecutando: docker-compose run --rm bmc-remedy-app python main.py %cedula%
        docker-compose run --rm bmc-remedy-app python main.py %cedula%
    ) else (
        echo Ejecutando: docker-compose run --rm bmc-remedy-app python main.py %cedula% --destinatario %email%
        docker-compose run --rm bmc-remedy-app python main.py %cedula% --destinatario %email%
    )
)

echo.
echo Script completado.
pause
