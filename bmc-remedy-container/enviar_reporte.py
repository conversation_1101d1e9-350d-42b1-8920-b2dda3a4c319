#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo optimizado para generar y enviar reportes HTML por correo.
Simplificado para mejor rendimiento y mantenibilidad.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

from mail import Mail
from logging_config import get_logger


def formatear_fecha(fecha_str: str) -> str:
    """
    Formatea una fecha de formato ISO a formato DD/MM/YYYY HH:MM en hora colombiana (UTC-5).
    
    Args:
        fecha_str: Fecha en formato ISO
        
    Returns:
        Fecha formateada o "N/A" si no es válida
    """
    if not fecha_str or fecha_str == "N/A":
        return "N/A"

    try:
        # Parsear la fecha ISO (ej: 2025-02-19T07:00:08.000+0000)
        if 'T' in fecha_str:
            fecha_parte = fecha_str.split('T')[0]
            hora_parte = fecha_str.split('T')[1].split('.')[0] if '.' in fecha_str else fecha_str.split('T')[1].split('+')[0]

            # Convertir a datetime (asumiendo que viene en UTC)
            fecha_obj = datetime.strptime(f"{fecha_parte} {hora_parte}", "%Y-%m-%d %H:%M:%S")

            # Convertir a hora colombiana (UTC-5)
            fecha_colombia = fecha_obj - timedelta(hours=5)

            # Formatear como DD/MM/YYYY HH:MM (Hora Colombia)
            return fecha_colombia.strftime("%d/%m/%Y %H:%M (COL)")
        else:
            return fecha_str
    except Exception:
        return fecha_str


def formatear_estado(status: str) -> str:
    """
    Aplica un estilo al estado según su valor.
    
    Args:
        status: Estado del ticket/incidente
        
    Returns:
        Estado con estilo HTML aplicado
    """
    if status and isinstance(status, str):
        if status.lower() in ['resuelto', 'resolved', 'closed', 'baja', 'low']:
            return f'<span style="color: green; font-weight: bold;">{status}</span>'
        elif status.lower() in ['pendiente', 'pending', 'media', 'medium']:
            return f'<span style="color: orange; font-weight: bold;">{status}</span>'
        elif status.lower() in ['abierto', 'assigned', 'open', 'alta', 'high']:
            return f'<span style="color: red; font-weight: bold;">{status}</span>'
    return status or "N/A"


def formatear_valor(value: Any) -> str:
    """
    Formatea un valor para mejor visualización.
    
    Args:
        value: Valor a formatear
        
    Returns:
        Valor formateado como string
    """
    if value is None:
        return "N/A"
    elif isinstance(value, dict):
        return ", ".join([f"{k}: {v}" for k, v in value.items() if v is not None and v != "N/A"])
    elif value == "":
        return "N/A"
    return str(value)


def crear_tabla_html(items: List[Dict[str, Any]], tipo: str) -> str:
    """
    Crea una tabla HTML para tickets o incidentes.

    Args:
        items: Lista de tickets o incidentes
        tipo: "tickets" o "incidentes"

    Returns:
        HTML de la tabla
    """
    if not items:
        return f"<p>No se encontraron {tipo}.</p>"

    if tipo == "incidentes":
        return crear_incidentes_detallados_html(items)
    else:
        return crear_tickets_simples_html(items, tipo)


def crear_tickets_simples_html(items: List[Dict[str, Any]], tipo: str) -> str:
    """Crea tabla simple para tickets."""
    html = f'''
    <h2>{tipo.title()} ({len(items)})</h2>
    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <thead>
            <tr style="background-color: #f8f9fa;">
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">ID</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Estado</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Prioridad</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Descripción</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Última Modificación</th>
            </tr>
        </thead>
        <tbody>
    '''

    for item in items:
        request_id = formatear_valor(item.get('request_id'))
        status = formatear_estado(item.get('status'))
        priority = formatear_valor(item.get('priority'))
        description = formatear_valor(item.get('detailed_description', ''))

        # Truncar descripción si es muy larga
        if len(description) > 100:
            description = description[:100] + "..."

        last_modified = formatear_fecha(item.get('last_modified_date'))

        html += f'''
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">{request_id}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">{status}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">{priority}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">{description}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">{last_modified}</td>
            </tr>
        '''

    html += '''
        </tbody>
    </table>
    '''

    return html


def crear_incidentes_detallados_html(incidentes: List[Dict[str, Any]]) -> str:
    """
    Crea HTML detallado para incidentes con formato de tabla individual por incidente.

    Args:
        incidentes: Lista de incidentes

    Returns:
        HTML detallado de los incidentes
    """
    html = f'<h2>Incidentes ({len(incidentes)})</h2>'

    for i, incidente in enumerate(incidentes, 1):
        incident_id = formatear_valor(incidente.get('incident_id', incidente.get('request_id', 'Sin ID')))

        # Encabezado del incidente
        html += f'''
        <div style="margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px;">
            <div style="background-color: #4a90e2; color: white; padding: 10px; border-radius: 5px 5px 0 0;">
                <h3 style="margin: 0;">Incidente #{i}: {incident_id}</h3>
            </div>

            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #2c5282; color: white;">
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left; width: 25%;">Campo</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Valor</th>
                    </tr>
                </thead>
                <tbody>
        '''

        # Campos principales del incidente
        campos = [
            ('DWP', incidente.get('dwp_number')),
            ('Resumen', incidente.get('resumen')),
            ('Estado', incidente.get('status')),
            ('Descripción', incidente.get('detailed_description')),
            ('Última modificación', formatear_fecha(incidente.get('last_modified_date'))),
            ('Fecha de creación', formatear_fecha(incidente.get('create_date'))),
            ('Nombre del cliente', incidente.get('customer_name') or incidente.get('customer')),
            ('Resolución', incidente.get('resolution'))
        ]

        for campo, valor in campos:
            valor_formateado = formatear_valor(valor)
            if campo == 'Estado':
                valor_formateado = formatear_estado(valor)

            html += f'''
                <tr style="background-color: {'#f8f9fa' if campos.index((campo, valor)) % 2 == 0 else 'white'};">
                    <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #4a90e2;">{campo}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{valor_formateado}</td>
                </tr>
            '''

        html += '''
                </tbody>
            </table>
        '''

        # Agregar notas si existen
        notas = incidente.get('notas', [])
        if notas and len(notas) > 0:
            # Mostrar solo la primera nota (la más reciente)
            nota = notas[0]
            html += f'''
            <div style="background-color: #e8f4fd; padding: 10px; margin-top: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #2c5282;">Última nota</h4>
                <table style="width: 100%; border-collapse: collapse;">
                    <tbody>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #4a90e2; width: 25%;">Número de Nota</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">{formatear_valor(nota.get('note_number'))}</td>
                        </tr>
                        <tr style="background-color: #f8f9fa;">
                            <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #4a90e2;">Resumen</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">{formatear_valor(nota.get('resumen'))}</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #4a90e2;">Descripción Detallada</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">{formatear_valor(nota.get('detailed_description'))}</td>
                        </tr>
                        <tr style="background-color: #f8f9fa;">
                            <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #4a90e2;">Fecha de Modificación</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">{formatear_fecha(nota.get('last_modified_date'))}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            '''

        html += '</div>'

    return html


def crear_reporte_html(json_file: str) -> str:
    """
    Crea un reporte HTML a partir del archivo JSON unificado.
    
    Args:
        json_file: Ruta al archivo JSON
        
    Returns:
        HTML del reporte
    """
    logger = get_logger('enviar_reporte.crear_html')
    
    try:
        # Verificar si el archivo existe
        if not os.path.exists(json_file):
            return f"<p>Error: El archivo {json_file} no existe.</p>"
        
        # Cargar el archivo JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Obtener información del usuario
        user_info = data.get('user_info', {})
        # Construir nombre completo desde first_name y last_name o usar full_name
        first_name = user_info.get('first_name', '')
        last_name = user_info.get('last_name', '')
        full_name = formatear_valor(user_info.get('full_name') or f"{first_name} {last_name}".strip())
        email = formatear_valor(user_info.get('email') or user_info.get('Internet E-mail'))
        organization = formatear_valor(user_info.get('Organization'))
        
        # Obtener información de tickets
        tickets_info = data.get('tickets', {})
        cedula = formatear_valor(tickets_info.get('cedula'))
        fecha_consulta = formatear_valor(tickets_info.get('fecha_consulta'))
        tickets = tickets_info.get('data', [])
        
        # Obtener información de incidentes
        incidents_info = data.get('incidents', {})
        incidentes = incidents_info.get('data', [])
        
        # Iniciar el HTML con estilos
        fecha_actual = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        html = f'''
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; color: #333; line-height: 1.6; }}
                .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                h1 {{ color: #2c5282; border-bottom: 2px solid #2c5282; padding-bottom: 10px; }}
                h2 {{ color: #2d3748; margin-top: 30px; }}
                .info-section {{ background-color: #f7fafc; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                .summary {{ background-color: #e6fffa; padding: 10px; border-radius: 5px; margin-bottom: 20px; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f8f9fa; font-weight: bold; }}
                .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Reporte Unificado de Tickets e Incidentes</h1>
                
                <div class="info-section">
                    <h2>Información del Usuario</h2>
                    <p><strong>Cédula:</strong> {cedula}</p>
                    <p><strong>Nombre:</strong> {full_name}</p>
                    <p><strong>Email:</strong> {email}</p>
                    <p><strong>Organización:</strong> {organization}</p>
                    <p><strong>Fecha de consulta:</strong> {fecha_consulta}</p>
                </div>
                
                <div class="summary">
                    <h2>Resumen</h2>
                    <p><strong>Total de tickets:</strong> {len(tickets)}</p>
                    <p><strong>Total de incidentes:</strong> {len(incidentes)}</p>
                </div>
        '''
        
        # Agregar tabla de tickets
        html += crear_tabla_html(tickets, "tickets")
        
        # Agregar tabla de incidentes
        html += crear_tabla_html(incidentes, "incidentes")
        
        # Cerrar HTML
        html += f'''
                <div class="footer">
                    <p>Reporte generado automáticamente el {fecha_actual}</p>
                    <p>Sistema de Gestión de Tickets BMC Remedy</p>
                </div>
            </div>
        </body>
        </html>
        '''
        
        logger.info(f"Reporte HTML generado exitosamente - tickets: {len(tickets)}, incidentes: {len(incidentes)}")
        return html
        
    except json.JSONDecodeError as e:
        logger.error(f"Error al decodificar JSON: {str(e)}")
        return f"<p>Error: El archivo JSON no es válido.</p>"
    except Exception as e:
        logger.error(f"Error al crear reporte HTML: {str(e)}")
        return f"<p>Error al generar el reporte: {str(e)}</p>"


def enviar_reporte(json_file: str, destinatario: str = None) -> bool:
    """
    Función principal para enviar el reporte.
    
    Args:
        json_file: Ruta al archivo JSON
        destinatario: Email del destinatario
        
    Returns:
        True si el envío fue exitoso, False en caso contrario
    """
    logger = get_logger('enviar_reporte')
    
    try:
        # Si no se especifica destinatario, usar el de por defecto
        if not destinatario:
            destinatario = '<EMAIL>'
        
        logger.info(f"Enviando reporte a {destinatario}")
        
        # Generar el HTML del reporte
        mensaje_html = crear_reporte_html(json_file)
        
        # Enviar el correo
        mail = Mail(destinatario)
        mail.send(
            subject="Reporte Unificado de Tickets",
            message=mensaje_html
        )
        
        logger.info(f"Reporte enviado correctamente a {destinatario}")
        return True
        
    except Exception as e:
        logger.error(f"Error al enviar el reporte: {str(e)}")
        return False


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Uso: python enviar_reporte.py <archivo_json> [destinatario]")
        sys.exit(1)
    
    json_file = sys.argv[1]
    destinatario = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Setup logging
    from logging_config import setup_logging
    setup_logging()
    
    # Enviar reporte
    resultado = enviar_reporte(json_file, destinatario)
    
    if not resultado:
        sys.exit(1)
