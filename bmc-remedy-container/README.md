# BMC Remedy Container

Este proyecto containeriza la aplicación de consulta y reporte de casos de BMC Remedy.

## Características

- Búsqueda de tickets e incidentes en BMC Remedy por cédula
- Generación de reportes unificados en formato HTML
- Envío automático de reportes por correo electrónico
- Títulos de notas traducidos al español
- **Fechas convertidas a hora colombiana (UTC-5)**
- Ejecución en contenedor Docker

## Requisitos

- Docker
- Docker Compose

## Configuración

1. Copie el archivo de configuración de ejemplo:
   ```bash
   cp .env.example .env
   ```

2. Edite el archivo `.env` con sus credenciales y configuración:
   ```
   LOGIN_URL=https://your-bmc-server.com/api/jwt/login
   API_BASE_URL=https://your-bmc-server.com/api
   USERNAME_=your_username
   PASSWORD=your_password
   EMAIL_TO=<EMAIL>
   ```

## Construcción y Ejecución

### Construir la imagen
```bash
docker-compose build
```

### Ejecutar una consulta
```bash
# Consultar casos para una cédula específica
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL>

# Consultar todos los incidentes
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL> --todos
```

### Modo desarrollo
```bash
# Iniciar contenedor en modo desarrollo
docker-compose --profile dev up -d bmc-remedy-dev

# Ejecutar comandos en el contenedor de desarrollo
docker-compose exec bmc-remedy-dev python main.py 1152213619 --destinatario <EMAIL>
```

## Estructura del Proyecto

```
bmc-remedy-container/
├── Dockerfile              # Definición de la imagen Docker
├── docker-compose.yml      # Configuración de servicios
├── requirements.txt        # Dependencias de Python
├── .env.example            # Archivo de configuración de ejemplo
├── README.md               # Este archivo
├── main.py                 # Script principal
├── open.py                 # Módulo de búsqueda en BMC
├── procesar_todo.py        # Procesamiento de reportes
├── enviar_reporte.py       # Generación y envío de reportes
├── mail.py                 # Módulo de envío de correos
├── a.py                    # Gestor de tickets BMC
└── logs/                   # Directorio de logs (montado como volumen)
```

## Uso

### Comando básico
```bash
docker-compose run --rm bmc-remedy-app python main.py <cedula> [opciones]
```

### Opciones disponibles
- `--destinatario <email>`: Especifica el destinatario del reporte
- `--todos`: Muestra todos los incidentes (no solo los primeros 5)

### Ejemplos
```bash
# Consulta básica
docker-compose run --rm bmc-remedy-app python main.py 1152213619

# Consulta con destinatario específico
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL>

# Consulta mostrando todos los incidentes
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --todos
```

## Logs

Los logs se almacenan en el directorio `./logs` y son persistentes entre ejecuciones del contenedor.

## Notas

- Los títulos de las notas ahora aparecen en español
- **Las fechas de creación y modificación se muestran en hora colombiana (UTC-5)**
- El contenedor incluye todas las dependencias necesarias
- La configuración se maneja a través de variables de entorno
- Los reportes se envían automáticamente por correo electrónico

## Conversión de Fechas

Las fechas en los reportes HTML se convierten automáticamente de UTC a hora colombiana:
- **UTC-5**: Colombia está 5 horas detrás de UTC
- **Formato**: DD/MM/YYYY HH:MM (COL)
- **Ejemplo**: 2025-02-16T01:29:50 UTC → 15/02/2025 20:29 (COL)
