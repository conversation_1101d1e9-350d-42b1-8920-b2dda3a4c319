#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo optimizado para búsqueda de tickets en BMC Remedy.
Simplificado y optimizado para mejor rendimiento.
"""

import json
import os
import sys
import datetime
import requests
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path

from a import TicketManager
from logging_config import get_logger
from display import create_displayer


def buscar_usuario_por_cedula(ticket_manager, cedula: str, logger) -> Dict[str, Any]:
    """
    Busca un usuario por su número de cédula en CTM:People.
    
    Args:
        ticket_manager: Instancia del TicketManager
        cedula: Número de cédula a buscar
        logger: Logger instance
        
    Returns:
        Dict con información del usuario encontrado
    """
    logger.info(f"Buscando usuario con cédula {cedula} en CTM:People...")
    
    try:
        api_base_url = ticket_manager.api_base_url
        url_usuario = f"{api_base_url}/arsys/v1/entry/CTM:People"
        
        params_usuario = {
            'q': f'\'Corporate ID\'="{cedula}"',
            'limit': '1'
        }
        
        logger.info(f"URL usuario: {url_usuario}")
        logger.info(f"Parámetros usuario: {params_usuario}")
        
        response = requests.get(url_usuario, headers=ticket_manager.headers, params=params_usuario)
        logger.info(f"Status code usuario: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Respuesta usuario: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('entries') and len(data['entries']) > 0:
                user_data = data['entries'][0]['values']
                return {
                    'found': True,
                    'first_name': user_data.get('First Name', ''),
                    'last_name': user_data.get('Last Name', ''),
                    'full_name': user_data.get('Full Name', ''),
                    'email': user_data.get('Internet E-mail', ''),
                    'user_data': user_data
                }
            else:
                logger.warning(f"No se encontró usuario con cédula {cedula}")
                return {'found': False}
        else:
            logger.error(f"Error buscando usuario: {response.status_code} - {response.text}")
            return {'found': False}
            
    except Exception as e:
        logger.error(f"Error buscando usuario: {str(e)}")
        return {'found': False}


def _crear_ticket_info(values: Dict[str, Any]) -> Dict[str, Any]:
    """
    Crea un diccionario con información estandarizada del ticket.

    Args:
        values: Valores del ticket desde la API

    Returns:
        Dict con información del ticket
    """
    # Procesar descripción detallada
    detailed_description = values.get('Detailed Decription', '') or values.get('Detailed Description', '') or ''
    if isinstance(detailed_description, str):
        detailed_description = detailed_description.replace('\r\n', '\n').replace('\r', '\n')

    # Procesar customer
    customer = values.get('Customer', '') or ''
    if isinstance(customer, str):
        customer = customer.replace('\n', ' ').replace('\r', ' ')

    # Procesar resolución
    resolution = values.get('Resolution', '') or ''
    if isinstance(resolution, str):
        resolution = resolution.replace('\r\n', '\n').replace('\r', '\n')

    return {
        # Campos principales
        'request_id': values.get('Work Order ID') or values.get('Incident Number') or values.get('Incident ID'),
        'incident_id': values.get('Incident Number') or values.get('Entry ID'),
        'dwp_number': values.get('DWP_SRID') or values.get('Request ID') or 'N/A',
        'resumen': values.get('Description', '') or '',
        'status': values.get('Status'),
        'detailed_description': detailed_description.strip(),

        # Fechas
        'last_modified_date': values.get('Last Modified Date'),
        'create_date': values.get('Submit Date') or values.get('Create Date'),
        'closed_date': values.get('Closed Date'),

        # Asignación
        'assigned_to': values.get('Assigned To') or values.get('Assignee'),
        'assignee_groups': values.get('Assignee Groups'),
        'assigned_group': values.get('Assigned Group'),

        # Clasificación
        'priority': values.get('Priority'),
        'impact': values.get('Impact'),
        'urgency': values.get('Urgency'),
        'service_type': values.get('Service Type'),
        'service': values.get('Service'),

        # Ubicación y contacto
        'region': values.get('Region'),
        'site_group': values.get('Site Group'),
        'submitter': values.get('Submitter'),
        'customer': customer.strip(),
        'customer_name': values.get('First Name', '') + ' ' + values.get('Last Name', ''),
        'requestor_id': values.get('Requestor ID'),

        # Resolución
        'resolution': resolution.strip(),
        'resolution_method': values.get('Resolution Method'),
        'resolution_category': values.get('Resolution Category'),

        # Campos adicionales para compatibilidad
        'entry_id': values.get('Entry ID'),
        'person_id': values.get('Person ID'),
        'corporate_id': values.get('Corporate ID')
    }


def buscar_tickets_work_orders(ticket_manager, cedula: str, logger) -> List[Dict[str, Any]]:
    """
    Busca Work Orders para una cédula específica.
    
    Args:
        ticket_manager: Instancia del TicketManager
        cedula: Número de cédula
        logger: Logger instance
        
    Returns:
        Lista de tickets encontrados
    """
    logger.info(f"Buscando Work Orders para cédula {cedula}")
    tickets = []
    
    try:
        api_base_url = ticket_manager.api_base_url
        url_work_orders = f"{api_base_url}/arsys/v1/entry/WOI:WorkOrderInterface"
        
        # Buscar por diferentes campos posibles
        campos_busqueda = [
            f"'Customer Person ID'=\"{cedula}\"",
            f"'Person ID'=\"{cedula}\"",
            f"'Requestor ID'=\"{cedula}\""
        ]
        
        for campo_query in campos_busqueda:
            params_wo = {
                "q": campo_query,
                "limit": "1000"
            }
            
            logger.info(f"Consultando Work Orders con query: {campo_query}")
            response_wo = requests.get(url_work_orders, headers=ticket_manager.headers, params=params_wo)
            
            if response_wo.status_code == 200:
                data_wo = response_wo.json()
                if data_wo.get('entries'):
                    logger.info(f"Encontrados {len(data_wo['entries'])} Work Orders con {campo_query}")
                    
                    for entry in data_wo['entries']:
                        values = entry.get('values', {})
                        ticket_info = _crear_ticket_info(values)
                        
                        # Evitar duplicados usando un identificador único
                        entry_id = entry.get('_links', {}).get('self', [{}])[0].get('href', '')
                        if entry_id and not any(entry_id in str(t) for t in tickets):
                            tickets.append(ticket_info)
                        elif not entry_id:
                            # Si no hay entry_id, usar todos los campos para comparar
                            ticket_str = str(ticket_info)
                            if not any(ticket_str == str(t) for t in tickets):
                                tickets.append(ticket_info)
            else:
                logger.warning(f"Error consultando Work Orders: {response_wo.status_code}")
                
    except Exception as e:
        logger.error(f"Error buscando Work Orders: {str(e)}")
    
    return tickets


def obtener_notas_incidente(incident_id: str, ticket_manager: TicketManager) -> List[Dict[str, Any]]:
    """
    Obtiene las notas de un incidente específico.

    Args:
        incident_id: ID del incidente
        ticket_manager: Instancia del TicketManager

    Returns:
        Lista de notas del incidente
    """
    logger = get_logger(__name__)
    notas = []

    try:
        url_notas = f"{ticket_manager.api_base_url}/arsys/v1/entry/HPD:WorkLog"
        params = {
            "q": f"'Incident Number'=\"{incident_id}\"",
            "limit": "10"
        }

        response = requests.get(url_notas, headers=ticket_manager.headers, params=params)

        if response.status_code == 200:
            data = response.json()
            entries = data.get('entries', [])

            for entry in entries:
                values = entry.get('values', {})
                # Filtrar solo notas relevantes (no cambios de estado automáticos)
                if values.get('Work Log Type') != 'Status Change':
                    nota_info = {
                        'note_number': values.get('Work Log ID', ''),
                        'resumen': values.get('Short Description', 'Sin resumen'),
                        'detailed_description': values.get('Detailed Description', ''),
                        'last_modified_date': values.get('Last Modified Date', ''),
                        'submitter': values.get('Work Log Submitter', ''),
                        'communication_type': values.get('Communication Type', ''),
                        'view_access': values.get('View Access', '')
                    }
                    notas.append(nota_info)
        else:
            logger.warning(f"Error obteniendo notas para {incident_id}: {response.status_code}")

    except Exception as e:
        logger.error(f"Error obteniendo notas para {incident_id}: {str(e)}")

    return notas


def buscar_incidentes(ticket_manager, cedula: str, logger) -> List[Dict[str, Any]]:
    """
    Busca incidentes para una cédula específica.
    
    Args:
        ticket_manager: Instancia del TicketManager
        cedula: Número de cédula
        logger: Logger instance
        
    Returns:
        Lista de incidentes encontrados
    """
    logger.info(f"Buscando incidentes para cédula {cedula}")
    incidentes = []
    
    try:
        api_base_url = ticket_manager.api_base_url
        url_incidents = f"{api_base_url}/arsys/v1/entry/HPD:IncidentInterface"
        
        # Buscar por diferentes campos posibles
        campos_busqueda = [
            f"'Corporate ID'=\"{cedula}\"",
            f"'Direct Contact Corporate ID'=\"{cedula}\"",
            f"'Customer Person ID'=\"{cedula}\"",
            f"'Person ID'=\"{cedula}\""
        ]
        
        for campo_query in campos_busqueda:
            params_inc = {
                "q": campo_query,
                "limit": "1000"
            }
            
            logger.info(f"Consultando incidentes con query: {campo_query}")
            response_inc = requests.get(url_incidents, headers=ticket_manager.headers, params=params_inc)
            
            if response_inc.status_code == 200:
                data_inc = response_inc.json()
                if data_inc.get('entries'):
                    logger.info(f"Encontrados {len(data_inc['entries'])} incidentes con {campo_query}")
                    
                    for entry in data_inc['entries']:
                        values = entry.get('values', {})
                        incident_info = _crear_ticket_info(values)

                        # Obtener notas del incidente si tiene Incident Number
                        incident_number = incident_info.get('incident_id')
                        if incident_number:
                            notas = obtener_notas_incidente(incident_number, ticket_manager)
                            incident_info['notas'] = notas

                        # Evitar duplicados usando un identificador único
                        entry_id = entry.get('_links', {}).get('self', [{}])[0].get('href', '')
                        if entry_id and not any(entry_id in str(i) for i in incidentes):
                            incidentes.append(incident_info)
                        elif not entry_id:
                            # Si no hay entry_id, usar todos los campos para comparar
                            incident_str = str(incident_info)
                            if not any(incident_str == str(i) for i in incidentes):
                                incidentes.append(incident_info)
            else:
                logger.warning(f"Error consultando incidentes: {response_inc.status_code}")
                
    except Exception as e:
        logger.error(f"Error buscando incidentes: {str(e)}")
    
    return incidentes


def buscar_tickets_por_cedula(cedula: str, todos: bool = False) -> Optional[str]:
    """
    Busca tickets e incidentes para una cédula específica en BMC Remedy.

    Args:
        cedula: Número de cédula a consultar
        todos: Si es True, incluye todos los incidentes

    Returns:
        Path to the generated report file, or None if error
    """
    logger = get_logger('open.buscar_tickets')

    try:
        logger.info(f"Starting búsqueda de tickets - cedula: {cedula}, mostrar_todos: {todos}")

        # Crear instancia del TicketManager
        ticket_manager = TicketManager()

        # Paso 1: Buscar usuario por cédula para obtener información
        user_info = buscar_usuario_por_cedula(ticket_manager, cedula, logger)

        # Paso 2: Buscar tickets y incidentes
        tickets = buscar_tickets_work_orders(ticket_manager, cedula, logger)
        incidentes = buscar_incidentes(ticket_manager, cedula, logger)

        # Generar fecha de consulta
        fecha_consulta = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Crear el reporte unificado
        reporte = {
            "user_info": user_info.get('user_data', {}) if user_info['found'] else {},
            "tickets": {
                "cedula": cedula,
                "fecha_consulta": fecha_consulta,
                "total_tickets": len(tickets),
                "tickets_procesados": len(tickets),
                "data": tickets
            },
            "incidents": {
                "login_id": cedula,
                "fecha_consulta": fecha_consulta,
                "total_incidentes": len(incidentes),
                "incidentes_procesados": len(incidentes),
                "data": incidentes
            }
        }

        # Guardar el reporte como JSON
        reporte_file = os.path.join(os.path.dirname(__file__), "reporte_unificado.json")
        with open(reporte_file, 'w', encoding='utf-8') as f:
            json.dump(reporte, f, indent=2, ensure_ascii=False)

        logger.info(f"Reporte unificado generado correctamente: {reporte_file}")
        logger.info(f"Successfully completed búsqueda de tickets - tickets: {len(tickets)}, incidentes: {len(incidentes)}")

        return reporte_file

    except Exception as e:
        logger.error(f"Error al buscar tickets: {str(e)}")
        return None


def mostrar_reporte_unificado(json_file: str, mostrar_todos: bool = False):
    """
    Muestra el reporte unificado con tablas en la consola.

    Args:
        json_file: Path to the JSON report file
        mostrar_todos: Whether to show all incidents or limit to 5
    """
    logger = get_logger('open.mostrar_reporte')
    displayer = create_displayer()

    try:
        logger.info(f"Starting mostrar reporte - archivo: {json_file}, mostrar_todos: {mostrar_todos}")

        # Verificar si el archivo existe
        if not os.path.exists(json_file):
            displayer.display_error(f"El archivo {json_file} no existe.")
            return

        # Cargar el archivo JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Mostrar información de tickets
        _display_tickets_section(data, displayer, logger)

        # Mostrar información de incidentes
        _display_incidents_section(data, displayer, logger, mostrar_todos)

        logger.info(f"Successfully completed mostrar reporte")

    except json.JSONDecodeError as e:
        logger.error(f"Error mostrar reporte - JSON inválido: {str(e)}")
        displayer.display_error("El archivo no es un JSON válido.")
    except Exception as e:
        logger.error(f"Error mostrar reporte: {str(e)}")
        displayer.display_error(str(e))


def _display_tickets_section(data: Dict[str, Any], displayer, logger):
    """Display tickets section of the report."""
    if "tickets" not in data or not isinstance(data["tickets"], dict):
        return

    tickets_info = data["tickets"]
    cedula = tickets_info.get("cedula", "N/A")
    fecha_consulta = tickets_info.get("fecha_consulta", "N/A")
    total_tickets = tickets_info.get("total_tickets", 0)

    displayer.display_section_header("INFORMACIÓN DE TICKETS")
    displayer.display_info_line("Cédula", cedula)
    displayer.display_info_line("Fecha de consulta", fecha_consulta)
    displayer.display_info_line("Total de tickets", total_tickets)

    tickets = tickets_info.get("data", [])
    if tickets:
        priority_fields = ['request_id', 'status', 'priority', 'detailed_description', 
                          'assigned_to', 'last_modified_date', 'region', 'site_group']

        for i, ticket in enumerate(tickets):
            request_id = ticket.get('request_id', 'Sin ID')
            displayer.display_item_header("Ticket", i+1, request_id)

            # Create and display table
            table = displayer.create_item_table(ticket, priority_fields)
            displayer.display_table(table)
    else:
        displayer.display_warning("No se encontraron tickets.")


def _display_incidents_section(data: Dict[str, Any], displayer, logger, mostrar_todos: bool = False):
    """Display incidents section of the report."""
    if "incidents" not in data or not isinstance(data["incidents"], dict):
        return

    incidents_info = data["incidents"]
    login_id = incidents_info.get("login_id", "N/A")
    fecha_consulta = incidents_info.get("fecha_consulta", "N/A")
    total_incidentes = incidents_info.get("total_incidentes", 0)

    displayer.display_section_header("INFORMACIÓN DE INCIDENTES")
    displayer.display_info_line("Login ID", login_id)
    displayer.display_info_line("Fecha de consulta", fecha_consulta)
    displayer.display_info_line("Total de incidentes", total_incidentes)

    incidentes = incidents_info.get("data", [])
    if incidentes:
        # Limitar a 5 incidentes si no se especifica mostrar todos
        incidentes_a_mostrar = incidentes if mostrar_todos else incidentes[:5]
        
        priority_fields = ['incident_id', 'status', 'priority', 'dwp_number', 'detailed_description',
                          'assigned_to', 'last_modified_date', 'impact', 'urgency', 'service_type',
                          'region', 'site_group', 'resolution', 'assignee_groups', 'create_date',
                          'submitter', 'customer', 'requestor_id', 'service', 'resolution_method']

        for i, incidente in enumerate(incidentes_a_mostrar):
            incident_id = incidente.get('incident_id', incidente.get('request_id', 'Sin ID'))
            displayer.display_item_header("Incidente", i+1, incident_id)

            # Create and display table
            table = displayer.create_item_table(incidente, priority_fields)
            displayer.display_table(table)

            # Mostrar notas si existen
            notas = incidente.get('notas', [])
            if notas:
                displayer.display_note_header()
                # Mostrar solo la primera nota (la más reciente)
                nota = notas[0]
                note_table = displayer.create_note_table(nota)
                displayer.display_table(note_table)

        # Mostrar advertencia si hay más incidentes
        if not mostrar_todos and len(incidentes) > 5:
            displayer.display_warning(f"Mostrando 5 de {len(incidentes)} incidentes. Use '--todos' para ver todos.")
    else:
        displayer.display_warning("No se encontraron incidentes.")


if __name__ == "__main__":
    # Verificar argumentos
    if len(sys.argv) > 1:
        # Buscar tickets por cédula
        cedula = sys.argv[1]
        mostrar_todos = '--todos' in sys.argv
        
        # Ejecutar búsqueda y generar el reporte
        reporte_file = buscar_tickets_por_cedula(cedula, mostrar_todos)
        
        if reporte_file:
            # Mostrar el reporte en la consola
            mostrar_reporte_unificado(reporte_file, mostrar_todos)
    else:
        print(f"\nUso: python {sys.argv[0]} <cedula> [--todos]")
        print(f"  --todos: Muestra todos los incidentes (por defecto solo muestra los 5 primeros)")
